package com.example.biaozhu.controller;

import com.example.biaozhu.entity.User;
import com.example.biaozhu.payload.request.UserUpdateRequest;
import com.example.biaozhu.payload.response.UserResponse;
import com.example.biaozhu.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureTestMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 用户控制器测试类
 */
@SpringBootTest
@AutoConfigureTestMvc
public class UserControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private UserService userService;

    @Autowired
    private ObjectMapper objectMapper;

    private User testUser;
    private UserUpdateRequest updateRequest;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");
        testUser.setFullName("Test User");
        testUser.setBio("Test bio");
        testUser.setPhone("1234567890");

        updateRequest = new UserUpdateRequest();
        updateRequest.setEmail("<EMAIL>");
        updateRequest.setFullName("Updated User");
        updateRequest.setBio("Updated bio");
        updateRequest.setPhone("0987654321");
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testUpdateUserProfile_Success() throws Exception {
        // 模拟服务层返回更新后的用户
        User updatedUser = new User();
        updatedUser.setId(1L);
        updatedUser.setUsername("testuser");
        updatedUser.setEmail("<EMAIL>");
        updatedUser.setFullName("Updated User");
        updatedUser.setBio("Updated bio");
        updatedUser.setPhone("0987654321");

        when(userService.updateUserWithDTO(eq(1L), any(UserUpdateRequest.class)))
                .thenReturn(updatedUser);

        mockMvc.perform(put("/users/1/profile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"))
                .andExpect(jsonPath("$.fullName").value("Updated User"))
                .andExpect(jsonPath("$.bio").value("Updated bio"))
                .andExpect(jsonPath("$.phone").value("0987654321"));
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testUpdateUserProfile_InvalidEmail() throws Exception {
        updateRequest.setEmail("invalid-email");

        mockMvc.perform(put("/users/1/profile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("参数验证失败"))
                .andExpect(jsonPath("$.errors.email").exists());
    }

    @Test
    @WithMockUser(username = "testuser", roles = {"USER"})
    void testUpdateUserProfile_TooLongFullName() throws Exception {
        updateRequest.setFullName("a".repeat(51)); // 超过50个字符

        mockMvc.perform(put("/users/1/profile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("参数验证失败"))
                .andExpect(jsonPath("$.errors.fullName").exists());
    }

    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testUpdateUserProfile_AsAdmin() throws Exception {
        User updatedUser = new User();
        updatedUser.setId(1L);
        updatedUser.setUsername("testuser");
        updatedUser.setEmail("<EMAIL>");
        updatedUser.setFullName("Updated User");

        when(userService.updateUserWithDTO(eq(1L), any(UserUpdateRequest.class)))
                .thenReturn(updatedUser);

        mockMvc.perform(put("/users/1/profile")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(updateRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }
}
