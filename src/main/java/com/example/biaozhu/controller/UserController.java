package com.example.biaozhu.controller;

import com.example.biaozhu.entity.Role;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.exception.ResourceNotFoundException;
import com.example.biaozhu.payload.response.MessageResponse;
import com.example.biaozhu.payload.response.UserResponse;
import com.example.biaozhu.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户控制器，提供用户相关接口
 */
@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/users")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    /**
     * 获取当前用户信息
     * 
     * @return 用户信息响应
     */
    @GetMapping("/me")
    public ResponseEntity<?> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        User user = userService.getUserByUsername(authentication.getName());
        return ResponseEntity.ok(new UserResponse(user));
    }

    /**
     * 获取所有用户（分页）
     * 
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 用户分页数据
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "id") String sort) {
        
        logger.info("获取用户列表请求: page={}, size={}, sort={}", page, size, sort);
        try {
            // 输出当前用户信息和角色，帮助调试
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            logger.info("当前用户: {}, 权限: {}", auth.getName(), 
                       auth.getAuthorities().stream().map(Object::toString).collect(Collectors.joining(", ")));
            
            Page<User> usersPage = userService.getAllUsers(page, size, sort);
            
            Map<String, Object> response = new HashMap<>();
            response.put("users", usersPage.getContent().stream()
                    .map(UserResponse::new)
                    .collect(Collectors.toList()));
            response.put("currentPage", usersPage.getNumber());
            response.put("totalItems", usersPage.getTotalElements());
            response.put("totalPages", usersPage.getTotalPages());
            
            logger.info("成功获取用户列表，总数: {}", usersPage.getTotalElements());
            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            logger.error("获取用户列表失败", e);
            return new ResponseEntity<>(
                new MessageResponse("获取用户列表失败: " + e.getMessage(), false), 
                HttpStatus.INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * 根据ID获取用户
     * 
     * @param id 用户ID
     * @return 用户信息响应
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isCurrentUser(#id)")
    public ResponseEntity<?> getUserById(@PathVariable Long id) {
        try {
            User user = userService.getUserById(id);
            return ResponseEntity.ok(new UserResponse(user));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new MessageResponse("用户不存在: " + e.getMessage(), false));
        }
    }

    /**
     * 根据角色ID获取用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表响应
     */
    @GetMapping("/role/{roleId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getUsersByRole(@PathVariable Long roleId) {
        try {
            List<User> users = userService.getUsersByRole(roleId);
            List<UserResponse> userResponses = users.stream()
                    .map(UserResponse::new)
                    .collect(Collectors.toList());
            return ResponseEntity.ok(userResponses);
        } catch (Exception e) {
            logger.error("获取角色用户列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new MessageResponse("获取角色用户列表失败: " + e.getMessage(), false));
        }
    }

    /**
     * 更新用户信息
     *
     * @param id 用户ID
     * @param userDetails 用户详情
     * @return 更新后的用户信息响应
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isCurrentUser(#id)")
    public ResponseEntity<?> updateUser(@PathVariable Long id, @Valid @RequestBody User userDetails) {
        try {
            User updatedUser = userService.updateUser(id, userDetails);
            return ResponseEntity.ok(new UserResponse(updatedUser));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new MessageResponse("用户不存在: " + e.getMessage(), false));
        } catch (Exception e) {
            logger.error("更新用户信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new MessageResponse("更新用户信息失败: " + e.getMessage(), false));
        }
    }

    /**
     * 使用DTO更新用户信息（推荐使用）
     *
     * @param id 用户ID
     * @param updateRequest 用户更新请求DTO
     * @return 更新后的用户信息响应
     */
    @PutMapping("/{id}/profile")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isCurrentUser(#id)")
    public ResponseEntity<?> updateUserProfile(@PathVariable Long id,
                                             @Valid @RequestBody com.example.biaozhu.payload.request.UserUpdateRequest updateRequest) {
        logger.info("更新用户信息: userId={}, request={}", id, updateRequest);
        User updatedUser = userService.updateUserWithDTO(id, updateRequest);
        return ResponseEntity.ok(new UserResponse(updatedUser));
    }

    /**
     * 删除用户
     * 
     * @param id 用户ID
     * @return 删除成功响应
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.ok(new MessageResponse("用户删除成功", true));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new MessageResponse("用户不存在: " + e.getMessage(), false));
        } catch (Exception e) {
            logger.error("删除用户失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new MessageResponse("删除用户失败: " + e.getMessage(), false));
        }
    }

    /**
     * 更新用户角色
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 成功响应
     */
    @PutMapping("/{userId}/role/{roleId}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateUserRole(@PathVariable Long userId, @PathVariable Long roleId) {
        try {
            userService.updateUserRole(userId, roleId);
            return ResponseEntity.ok(new MessageResponse("用户角色更新成功", true));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new MessageResponse("更新失败: " + e.getMessage(), false));
        } catch (Exception e) {
            logger.error("更新用户角色失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new MessageResponse("更新用户角色失败: " + e.getMessage(), false));
        }
    }

    /**
     * 批量更新用户角色
     * 
     * @param userId 用户ID
     * @param rolesRequest 包含角色列表的请求体
     * @return 成功响应
     */
    @PutMapping("/{userId}/roles")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateUserRoles(
            @PathVariable Long userId, 
            @RequestBody Map<String, List<Map<String, Long>>> rolesRequest) {
        
        try {
            logger.info("收到更新用户角色请求: userId={}", userId);
            
            List<Map<String, Long>> rolesList = rolesRequest.get("roles");
            if (rolesList == null || rolesList.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("缺少角色参数", false));
            }
            
            // 提取所有角色ID
            Set<Long> roleIds = rolesList.stream()
                .filter(roleMap -> roleMap.containsKey("id"))
                .map(roleMap -> roleMap.get("id"))
                .collect(Collectors.toSet());
            
            logger.info("准备更新用户角色: userId={}, roleIds={}", userId, roleIds);
            
            // 更新用户角色
            User updatedUser = userService.updateUserRoles(userId, roleIds);
            
            return ResponseEntity.ok(new MessageResponse("用户角色更新成功", true));
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new MessageResponse("更新失败: " + e.getMessage(), false));
        } catch (Exception e) {
            logger.error("批量更新用户角色失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new MessageResponse("批量更新用户角色失败: " + e.getMessage(), false));
        }
    }

    /**
     * 获取用户统计信息
     * 
     * @param userId 用户ID
     * @return 用户统计信息
     */
    @GetMapping("/{userId}/statistics")
    @PreAuthorize("hasRole('ADMIN') or @securityService.isCurrentUser(#userId)")
    public ResponseEntity<?> getUserStatistics(@PathVariable Long userId) {
        try {
            Map<String, Object> statistics = userService.getUserStatistics(userId);
            return ResponseEntity.ok(statistics);
        } catch (ResourceNotFoundException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new MessageResponse("用户不存在: " + e.getMessage(), false));
        } catch (Exception e) {
            logger.error("获取用户统计信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new MessageResponse("获取用户统计信息失败: " + e.getMessage(), false));
        }
    }
    
    /**
     * 获取用户状态列表（用于下拉选择）
     */
    @GetMapping("/statuses")
    public ResponseEntity<?> getUserStatuses() {
        Map<String, String> statuses = new HashMap<>();
        statuses.put("ACTIVE", "已激活");
        statuses.put("INACTIVE", "已禁用");
        return ResponseEntity.ok(statuses);
    }
    
    /**
     * 更新用户状态
     */
    @PutMapping("/{userId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> updateUserStatus(
            @PathVariable Long userId, 
            @RequestBody Map<String, String> statusRequest) {
        
        try {
            String status = statusRequest.get("status");
            if (status == null) {
                return ResponseEntity.badRequest()
                    .body(new MessageResponse("缺少状态参数", false));
            }
            
            logger.info("更新用户状态: userId={}, newStatus={}", userId, status);
            
            // 使用专门的状态更新方法，而不是通用的updateUser方法
            User updatedUser = userService.updateUserStatus(userId, status);
            
            logger.info("用户状态已更新: userId={}, status={}, active={}", 
                       userId, updatedUser.getStatus(), updatedUser.isActive());
            
            return ResponseEntity.ok(
                new MessageResponse("用户状态已更新为: " + status, true));
        } catch (ResourceNotFoundException e) {
            logger.error("更新用户状态失败: 用户不存在", e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(new MessageResponse("用户不存在: " + e.getMessage(), false));
        } catch (Exception e) {
            logger.error("更新用户状态失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new MessageResponse("更新用户状态失败: " + e.getMessage(), false));
        }
    }
} 