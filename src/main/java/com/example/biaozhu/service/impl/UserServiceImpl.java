package com.example.biaozhu.service.impl;

import com.example.biaozhu.entity.Role;
import com.example.biaozhu.entity.Task;
import com.example.biaozhu.entity.User;
import com.example.biaozhu.exception.BadRequestException;
import com.example.biaozhu.exception.ResourceNotFoundException;
import com.example.biaozhu.repository.RoleRepository;
import com.example.biaozhu.repository.TaskRepository;
import com.example.biaozhu.repository.UserRepository;
import com.example.biaozhu.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final TaskRepository taskRepository;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserServiceImpl(UserRepository userRepository, 
                          RoleRepository roleRepository,
                          TaskRepository taskRepository,
                          PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.taskRepository = taskRepository;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    @Override
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    @Override
    public Optional<User> findByUsernameOrEmail(String usernameOrEmail) {
        return userRepository.findByUsernameOrEmail(usernameOrEmail, usernameOrEmail);
    }

    @Override
    public Page<User> findAllUsers(Pageable pageable) {
        return userRepository.findAll(pageable);
    }

    @Override
    public List<User> findByRoleId(Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with id: " + roleId));
        return userRepository.findByRoles(role);
    }

    @Override
    @Transactional
    public User createUser(User user) {
        // 对密码进行加密
        user.setPassword(passwordEncoder.encode(user.getPassword()));
        
        // 如果没有指定角色，设置为默认角色（访客）
        if (user.getRoles() == null || user.getRoles().isEmpty()) {
            Role defaultRole = roleRepository.findByName(Role.ERole.USER)
                    .orElseThrow(() -> new ResourceNotFoundException("Default role not found"));
            user.getRoles().add(defaultRole);
        }
        
        return userRepository.save(user);
    }

    @Override
    @Transactional
    public User updateUser(Long id, User userDetails) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
        
        user.setFullName(userDetails.getFullName());
        user.setEmail(userDetails.getEmail());
        user.setBio(userDetails.getBio());
        user.setPhone(userDetails.getPhone());
        
        // 如果提供了新密码，则更新密码
        if (userDetails.getPassword() != null && !userDetails.getPassword().isEmpty()) {
            user.setPassword(passwordEncoder.encode(userDetails.getPassword()));
        }
        
        // 如果提供了角色，则更新角色
        if (userDetails.getRoles() != null && !userDetails.getRoles().isEmpty()) {
            // 清除现有角色
            user.getRoles().clear();
            
            // 添加新角色
            for (Role role : userDetails.getRoles()) {
                if (role.getId() != null) {
                    // 如果提供了角色ID，则查找并添加对应角色
                    roleRepository.findById(role.getId())
                            .ifPresent(user.getRoles()::add);
                } else if (role.getName() != null) {
                    // 如果提供了角色名，则查找并添加对应角色
                    roleRepository.findByName(role.getName())
                            .ifPresent(user.getRoles()::add);
                }
            }
            
            // 如果没有成功添加任何角色，则添加默认USER角色
            if (user.getRoles().isEmpty()) {
                roleRepository.findByName(Role.ERole.USER)
                        .ifPresent(user.getRoles()::add);
            }
        }
        
        return userRepository.save(user);
    }

    @Override
    @Transactional
    public User updateUserWithDTO(Long id, com.example.biaozhu.payload.request.UserUpdateRequest updateRequest) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));

        // 检查邮箱唯一性（如果邮箱发生变化）
        if (updateRequest.getEmail() != null && !updateRequest.getEmail().equals(user.getEmail())) {
            if (userRepository.existsByEmail(updateRequest.getEmail())) {
                throw new BadRequestException("邮箱已被其他用户使用");
            }
            user.setEmail(updateRequest.getEmail());
        }

        // 更新其他字段
        if (updateRequest.getFullName() != null) {
            user.setFullName(updateRequest.getFullName());
        }

        if (updateRequest.getBio() != null) {
            user.setBio(updateRequest.getBio());
        }

        if (updateRequest.getPhone() != null) {
            user.setPhone(updateRequest.getPhone());
        }

        if (updateRequest.getAvatarUrl() != null) {
            user.setAvatarUrl(updateRequest.getAvatarUrl());
        }

        if (updateRequest.getPosition() != null) {
            user.setPosition(updateRequest.getPosition());
        }

        // 如果提供了新密码，则更新密码
        if (updateRequest.getPassword() != null && !updateRequest.getPassword().trim().isEmpty()) {
            user.setPassword(passwordEncoder.encode(updateRequest.getPassword()));
        }

        // 如果提供了团队ID，则更新团队
        if (updateRequest.getTeamId() != null) {
            if (updateRequest.getTeamId() > 0) {
                // 验证团队是否存在
                teamRepository.findById(updateRequest.getTeamId())
                        .ifPresentOrElse(
                                user::setTeam,
                                () -> { throw new ResourceNotFoundException("Team not found with id: " + updateRequest.getTeamId()); }
                        );
            } else {
                // 如果teamId为0或负数，则移除团队关联
                user.setTeam(null);
            }
        }

        return userRepository.save(user);
    }

    @Override
    @Transactional
    public void deleteUser(Long id) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
        
        userRepository.delete(user);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    @Override
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    public Map<String, Object> getUserStatistics(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取用户的任务统计信息
        Long assignedTasksCount = taskRepository.countByAssignedTo(user);
        
        // 由于TaskRepository可能没有实现这些方法，这里使用默认值
        Long completedTasksCount = 0L;
        Long pendingReviewCount = 0L;
        
        try {
            // 如果方法存在，则调用
            List<Task> completedTasks = taskRepository.findByReviewerAndStatus(user, Task.TaskStatus.COMPLETED);
            completedTasksCount = completedTasks != null ? (long)completedTasks.size() : 0L;
            
            List<Task> underReviewTasks = taskRepository.findByReviewerAndStatus(user, Task.TaskStatus.UNDER_REVIEW);
            pendingReviewCount = underReviewTasks != null ? (long)underReviewTasks.size() : 0L;
        } catch (Exception e) {
            // 忽略方法不存在的异常
        }
        
        statistics.put("assignedTasksCount", assignedTasksCount);
        statistics.put("completedTasksCount", completedTasksCount);
        statistics.put("pendingReviewCount", pendingReviewCount);
        statistics.put("completionRate", assignedTasksCount > 0 ? 
                (double) completedTasksCount / assignedTasksCount : 0);
        
        return statistics;
    }

    @Override
    @Transactional
    public User updateUserRole(Long userId, Long roleId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with id: " + roleId));
        
        user.getRoles().clear();
        user.getRoles().add(role);
        
        return userRepository.save(user);
    }
    
    @Override
    public User getUserById(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + id));
    }
    
    @Override
    public User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with username: " + username));
    }
    
    @Override
    public Page<User> getAllUsers(int page, int size, String sort) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, sort));
        
        // 先获取所有用户
        Page<User> allUsers = userRepository.findAll(pageable);
        
        // 过滤掉管理员用户
        // 由于Page对象不能直接修改，我们可以先转换为List并进行过滤
        List<User> filteredUsers = allUsers.getContent().stream()
                .filter(user -> !isAdminUser(user))
                .collect(Collectors.toList());
        
        // 创建新的Page对象
        return new org.springframework.data.domain.PageImpl<>(
                filteredUsers, 
                pageable, 
                userRepository.count() - countAdminUsers()
        );
    }
    
    /**
     * 判断用户是否为管理员
     * @param user 用户
     * @return 是否为管理员
     */
    private boolean isAdminUser(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.ERole.ADMIN);
    }
    
    /**
     * 统计管理员用户数量
     * @return 管理员数量
     */
    private long countAdminUsers() {
        // 获取Admin角色
        Optional<Role> adminRole = roleRepository.findByName(Role.ERole.ADMIN);
        if (adminRole.isPresent()) {
            // 获取该角色下的用户数量
            return userRepository.countByRolesId(adminRole.get().getId());
        }
        return 0;
    }
    
    /**
     * 根据角色名称获取用户
     * @param roleName 角色名称
     * @return 用户列表
     */
    @Override
    public List<User> getUsersByRole(String roleName) {
        try {
            Role.ERole roleEnum = Role.ERole.valueOf(roleName);
            return userRepository.findByRoleName(roleEnum.name());
        } catch (IllegalArgumentException e) {
            // 如果roleName不是有效的枚举值，则尝试直接使用字符串
            return userRepository.findByRoleName(roleName);
        }
    }
    
    @Override
    public List<User> getUsersByRole(Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with id: " + roleId));
        return userRepository.findByRoles(role);
    }
    
    /**
     * 批量更新用户角色 - 修改为只支持单个角色
     */
    @Override
    @Transactional
    public User updateUserRoles(Long userId, Set<Long> roleIds) {
        // 获取用户
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        // 清除现有角色
        user.getRoles().clear();
        
        // 添加新角色 - 只添加第一个角色ID
        if (roleIds != null && !roleIds.isEmpty()) {
            Long roleId = roleIds.iterator().next(); // 获取第一个角色ID
            roleRepository.findById(roleId)
                    .ifPresent(user.getRoles()::add);
        }
        
        // 如果没有成功添加任何角色，则添加默认USER角色
        if (user.getRoles().isEmpty()) {
            roleRepository.findByName(Role.ERole.USER)
                    .ifPresent(user.getRoles()::add);
        }
        
        return userRepository.save(user);
    }
    
    /**
     * 专门用于更新用户状态，避免使用通用的updateUser方法
     * 
     * @param userId 用户ID
     * @param status 新状态 ("ACTIVE" 或 "INACTIVE")
     * @return 更新后的用户
     */
    @Override
    @Transactional
    public User updateUserStatus(Long userId, String status) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        // 设置status字段
        user.setStatus(status);
        
        // 同时更新active字段，保持状态一致性
        boolean isActive = "ACTIVE".equals(status);
        user.setActive(isActive);
        
        // 直接保存并返回更新后的用户
        return userRepository.save(user);
    }
} 