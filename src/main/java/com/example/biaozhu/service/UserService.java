package com.example.biaozhu.service;

import com.example.biaozhu.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 用户服务接口，提供用户相关的业务逻辑功能
 */
public interface UserService {
    
    /**
     * 根据ID查找用户
     * @param id 用户ID
     * @return 用户对象
     */
    Optional<User> findById(Long id);
    
    /**
     * 根据用户名查找用户
     * @param username 用户名
     * @return 用户对象
     */
    Optional<User> findByUsername(String username);
    
    /**
     * 根据用户名或邮箱查找用户
     * @param usernameOrEmail 用户名或邮箱
     * @return 用户对象
     */
    Optional<User> findByUsernameOrEmail(String usernameOrEmail);
    
    /**
     * 分页获取所有用户
     * @param pageable 分页参数
     * @return 用户分页结果
     */
    Page<User> findAllUsers(Pageable pageable);
    
    /**
     * 根据角色查找用户
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<User> findByRoleId(Long roleId);
    
    /**
     * 创建新用户
     * @param user 用户对象
     * @return 保存后的用户对象
     */
    User createUser(User user);
    
    /**
     * 更新用户信息
     * @param id 用户ID
     * @param userDetails 用户详细信息
     * @return 更新后的用户对象
     */
    User updateUser(Long id, User userDetails);

    /**
     * 使用DTO更新用户信息
     * @param id 用户ID
     * @param updateRequest 用户更新请求DTO
     * @return 更新后的用户对象
     */
    User updateUserWithDTO(Long id, com.example.biaozhu.payload.request.UserUpdateRequest updateRequest);
    
    /**
     * 删除用户
     * @param id 用户ID
     */
    void deleteUser(Long id);
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    boolean existsByUsername(String username);
    
    /**
     * 检查邮箱是否存在
     * @param email 邮箱
     * @return 是否存在
     */
    boolean existsByEmail(String email);
    
    /**
     * 获取用户统计信息
     * @param userId 用户ID
     * @return 统计信息映射
     */
    java.util.Map<String, Object> getUserStatistics(Long userId);
    
    /**
     * 更新用户角色
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 更新后的用户对象
     */
    User updateUserRole(Long userId, Long roleId);
    
    /**
     * 根据ID获取用户，如果不存在则抛出异常
     * @param id 用户ID
     * @return 用户对象
     * @throws com.example.biaozhu.exception.ResourceNotFoundException 如果用户不存在
     */
    User getUserById(Long id);
    
    /**
     * 根据用户名获取用户，如果不存在则抛出异常
     * @param username 用户名
     * @return 用户对象
     * @throws com.example.biaozhu.exception.ResourceNotFoundException 如果用户不存在
     */
    User getUserByUsername(String username);
    
    /**
     * 获取所有用户，支持分页和排序
     * @param page 页码
     * @param size 每页大小
     * @param sort 排序字段
     * @return 用户分页结果
     */
    Page<User> getAllUsers(int page, int size, String sort);
    
    /**
     * 根据角色ID获取用户
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<User> getUsersByRole(Long roleId);
    
    /**
     * 根据角色名称获取用户
     * @param roleName 角色名称
     * @return 用户列表
     */
    List<User> getUsersByRole(String roleName);
    
    /**
     * 批量更新用户角色
     * @param userId 用户ID
     * @param roleIds 角色ID集合
     * @return 更新后的用户对象
     */
    User updateUserRoles(Long userId, java.util.Set<Long> roleIds);
    
    /**
     * 专门用于更新用户状态
     * @param userId 用户ID
     * @param status 新状态 ("ACTIVE" 或 "INACTIVE")
     * @return 更新后的用户对象
     */
    User updateUserStatus(Long userId, String status);
} 