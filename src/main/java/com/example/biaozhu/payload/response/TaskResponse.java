package com.example.biaozhu.payload.response;

import com.example.biaozhu.entity.Task;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务响应类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TaskResponse {
    
    /**
     * 任务ID
     */
    private Long id;
    
    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 任务描述
     */
    private String description;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 开始日期
     */
    private LocalDateTime startDate;
    
    /**
     * 截止日期
     */
    private LocalDateTime dueDate;
    
    /**
     * 完成日期
     */
    private LocalDateTime completionDate;
    
    /**
     * 数据集ID
     */
    private Long datasetId;
    
    /**
     * 数据集名称
     */
    private String datasetName;
    
    /**
     * 创建者摘要
     */
    private UserSummary creator;
    
    /**
     * 分配给用户摘要
     */
    private UserSummary assignedTo;
    
    /**
     * 审核人摘要
     */
    private UserSummary reviewer;
    
    /**
     * 标注数量
     */
    private Integer annotationCount;

    /**
     * 已完成的标注数量
     */
    private Integer completedAnnotations;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 标注模板ID
     */
    private Long templateId;
    
    /**
     * 标注模板名称
     */
    private String templateName;
    
    /**
     * 任务完成率
     */
    private Double completionRate;
    
    /**
     * 任务数据项数量
     */
    private Integer dataItemCount;
    
    /**
     * 指定数据项ID列表
     */
    private List<Long> dataItemIds;
    
    /**
     * 指定标签ID列表
     */
    private List<Long> labelIds;
    
    /**
     * 任务指南
     */
    private String guidelines;
    
    /**
     * 从Task实体构造TaskResponse
     * 
     * @param task 任务实体
     */
    public TaskResponse(Task task) {
        this.id = task.getId();
        this.name = task.getName();
        this.description = task.getDescription();
        this.status = task.getStatus();
        this.taskType = task.getTaskType();
        this.priority = task.getPriority();
        this.startDate = task.getStartDate();
        this.dueDate = task.getDueDate();
        this.completionDate = task.getCompletionDate();
        
        if (task.getDataset() != null) {
            this.datasetId = task.getDataset().getId();
            this.datasetName = task.getDataset().getName();
        }
        
        if (task.getCreator() != null) {
            this.creator = new UserSummary(
                task.getCreator().getId(),
                task.getCreator().getUsername(),
                task.getCreator().getFullName(),
                task.getCreator().getEmail()
            );
        }
        
        if (task.getAssignedTo() != null) {
            this.assignedTo = new UserSummary(
                task.getAssignedTo().getId(),
                task.getAssignedTo().getUsername(),
                task.getAssignedTo().getFullName(),
                task.getAssignedTo().getEmail()
            );
        }
        
        if (task.getReviewer() != null) {
            this.reviewer = new UserSummary(
                task.getReviewer().getId(),
                task.getReviewer().getUsername(),
                task.getReviewer().getFullName(),
                task.getReviewer().getEmail()
            );
        }
        
        this.annotationCount = task.getAnnotationCount();
        this.completedAnnotations = 0; // 默认值，需要通过其他构造函数设置
        this.createdAt = task.getCreatedAt();
        this.updatedAt = task.getUpdatedAt();

        if (task.getAnnotationTemplate() != null) {
            this.templateId = task.getAnnotationTemplate().getId();
            this.templateName = task.getAnnotationTemplate().getName();
        }

        // 由于Task中可能没有直接存储这些字段，所以使用默认值或计算值
        this.completionRate = task.getProgress() != null ? task.getProgress() : 0.0;
        this.dataItemCount = 0; // 默认值，实际应用中应根据任务关联的数据项数量计算
    }

    /**
     * 从Task实体构造TaskResponse，包含已完成标注数量
     *
     * @param task 任务实体
     * @param completedAnnotations 已完成的标注数量
     */
    public TaskResponse(Task task, Integer completedAnnotations) {
        this(task); // 调用原有构造函数
        this.completedAnnotations = completedAnnotations;
    }
}